{"name": "b360", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}